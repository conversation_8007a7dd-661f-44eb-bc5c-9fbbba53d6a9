<script setup>
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
} from "@/Components/shadcn/ui/avatar";
import { Button } from "@/Components/shadcn/ui/button";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/Components/shadcn/ui/dropdown-menu";
import { SidebarMenuButton } from "@/Components/shadcn/ui/sidebar";
import { Icon } from "@iconify/vue";
import { Link, router } from "@inertiajs/vue3";
import { computed, inject } from "vue";

const route = inject("route");

// Props to determine if we're inside a sidebar context
const props = defineProps({
    variant: { type: String, default: "sidebar" }, // "sidebar" or "mobile"
});

// Check if we should use SidebarMenuButton or regular Button
const useSidebarButton = computed(() => props.variant === "sidebar");

function logout() {
    router.post(route("logout"));
}
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <SidebarMenuButton
                v-if="useSidebarButton"
                size="lg"
                class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
                <Avatar class="h-8 w-8 rounded-lg">
                    <AvatarImage
                        :src="$page.props.auth.user.profile_photo_url ?? ''"
                        :alt="$page.props.auth.user.name"
                    />
                    <AvatarFallback class="rounded-lg">
                        {{ $page.props.auth.user.name.charAt(0) }}
                    </AvatarFallback>
                </Avatar>
                <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{
                        $page.props.auth.user.name
                    }}</span>
                    <span class="truncate text-xs">Faculty Member</span>
                </div>
                <Icon icon="lucide:chevrons-up-down" class="ml-auto size-4" />
            </SidebarMenuButton>

            <Button
                v-else
                variant="ghost"
                class="w-full justify-start h-12 px-3 data-[state=open]:bg-accent data-[state=open]:text-accent-foreground"
            >
                <Avatar class="h-8 w-8 rounded-lg">
                    <AvatarImage
                        :src="$page.props.auth.user.profile_photo_url ?? ''"
                        :alt="$page.props.auth.user.name"
                    />
                    <AvatarFallback class="rounded-lg">
                        {{ $page.props.auth.user.name.charAt(0) }}
                    </AvatarFallback>
                </Avatar>
                <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{
                        $page.props.auth.user.name
                    }}</span>
                    <span class="truncate text-xs">Faculty Member</span>
                </div>
                <Icon icon="lucide:chevrons-up-down" class="ml-auto size-4" />
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
            class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
            side="right"
            align="end"
            :side-offset="4"
        >
            <DropdownMenuLabel class="p-0 font-normal">
                <div
                    class="flex items-center gap-2 px-1 py-1.5 text-left text-sm"
                >
                    <Avatar class="h-8 w-8 rounded-lg">
                        <AvatarImage
                            :src="$page.props.auth.user.profile_photo_url ?? ''"
                            :alt="$page.props.auth.user.name"
                        />
                        <AvatarFallback class="rounded-lg">
                            {{ $page.props.auth.user.name.charAt(0) }}
                        </AvatarFallback>
                    </Avatar>
                    <div class="grid flex-1 text-left text-sm leading-tight">
                        <span class="truncate font-semibold">{{
                            $page.props.auth.user.name
                        }}</span>
                        <span class="truncate text-xs">{{
                            $page.props.auth.user.email
                        }}</span>
                    </div>
                </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
                <DropdownMenuItem as-child>
                    <Link :href="route('profile.show')" class="cursor-pointer">
                        <Icon icon="lucide:settings" />
                        Settings
                    </Link>
                </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem @click="logout" class="cursor-pointer">
                <Icon icon="lucide:log-out" />
                Log out
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>
